<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>MetaAI</title><g clip-path="url(#lobe-icons-metaai-fill-0)" filter="url(#lobe-icons-metaai-fill-1)"><path clip-rule="evenodd" d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm0 3.627a8.373 8.373 0 100 16.746 8.373 8.373 0 000-16.746z" fill="url(#lobe-icons-metaai-fill-2)" fill-rule="evenodd"></path></g><defs><linearGradient gradientUnits="userSpaceOnUse" id="lobe-icons-metaai-fill-2" x1="24" x2="0" y1="0" y2="24"><stop offset=".13" stop-color="#FF97E3"></stop><stop offset=".18" stop-color="#D14FE1"></stop><stop offset=".338" stop-color="#0050E2"></stop><stop offset=".666" stop-color="#0050E2"></stop><stop offset=".809" stop-color="#00DDF4"></stop><stop offset=".858" stop-color="#23F8CC"></stop></linearGradient><clipPath id="lobe-icons-metaai-fill-0"><path d="M0 0h24v24H0z" fill="#fff"></path></clipPath><filter color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="24" id="lobe-icons-metaai-fill-1" width="24" x="0" y="0"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset></feOffset><feGaussianBlur stdDeviation=".75"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"></feColorMatrix><feBlend in2="shape" result="effect1_innerShadow_674_237"></feBlend></filter></defs></svg>