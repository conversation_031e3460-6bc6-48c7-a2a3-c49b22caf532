name: Issue Check Inactive

on:
  schedule:
    - cron: '0 0 */15 * *'

permissions:
  contents: read

jobs:
  issue-check-inactive:
    permissions:
      issues: write # for actions-cool/issues-helper to update issues
      pull-requests: write # for actions-cool/issues-helper to update PRs
    runs-on: ubuntu-latest
    steps:
      - name: check-inactive
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'check-inactive'
          token: ${{ secrets.GH_TOKEN }}
          inactive-label: 'Inactive'
          inactive-day: 30
