"""
配置文件 - 包含映射规则和系统配置
"""

import logging

# 日志配置
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 图标相关配置
ICON_BASE_PATH = "lobe-icons/packages/static-png/light"
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light"

# 厂商名称映射 - 将模型名称关键词映射到对应的图标文件名
VENDOR_MAPPING = {
    # OpenAI系列
    'gpt': 'openai',
    'openai': 'openai',
    'dall-e': 'dalle',
    'dalle': 'dalle',
    'o1': 'openai',
    'o3': 'openai',  # o3-mini 等模型
    'text-embedding': 'openai',  # text-embedding-3-small 等嵌入模型
    'omni-moderation': 'openai',  # omni-moderation-latest 等审核模型
    'tts': 'openai',  # tts-1 等语音合成模型
    
    # Anthropic系列
    'claude': 'claude',
    'anthropic': 'anthropic',
    
    # Google系列
    'gemini': 'gemini',
    'palm': 'palm',
    'bard': 'gemini',
    'google': 'google',
    'vertex': 'vertexai',
    'imagen': 'gemini',
    'chat-bison': 'palm',  # chat-bison-001 等 PaLM 模型
    'text-bison': 'palm',  # text-bison-001 等 PaLM 模型
    'bison': 'palm',  # 通用 bison 系列模型
    
    # 阿里系列
    'qwen': 'qwen',
    'qvq': 'qwen',  # QVQ 是阿里的推理模型
    'qwq': 'qwen',  # QWQ 也是阿里的推理模型
    'tongyi': 'qwen',
    'alibaba': 'alibaba',
    'alibabacloud': 'alibabacloud',
    
    # 百度系列
    'wenxin': 'wenxin',
    'baidu': 'baidu',
    'ernie': 'wenxin',
    
    # 腾讯系列
    'hunyuan': 'hunyuan',
    'tencent': 'tencent',
    
    # 字节跳动系列
    'doubao': 'doubao',
    'bytedance': 'bytedance',
    
    # DeepSeek系列
    'deepseek': 'deepseek',
    
    # 智谱系列
    'chatglm': 'chatglm',
    'glm': 'chatglm',
    'zhipu': 'zhipu',
    
    # 月之暗面系列
    'kimi': 'kimi',
    'moonshot': 'moonshot',
    
    # xAI系列
    'grok': 'grok',
    'xai': 'xai',
    
    # Meta系列
    'llama': 'meta',
    'meta': 'meta',
    
    # Mistral系列
    'mistral': 'mistral',
    
    # Cohere系列
    'cohere': 'cohere',
    
    # 其他厂商
    'yi': 'yi',
    'baichuan': 'baichuan',
    'internlm': 'internlm',
    'spark': 'spark',
    'minimax': 'minimax',
    'stepfun': 'stepfun',
    'siliconcloud': 'siliconcloud',
    'microsoft': 'microsoft',
    'azure': 'azure',
}

# 功能关键词映射 - 根据模型名称和描述推断功能标签
FUNCTION_KEYWORDS = {
    '推理模型': ['thinking', 'reasoning', 'r1', 'o1', 'qwq', 'qvq', 'reasoning'],
    '文生图': ['image', 'generation', 'dall-e', 'dalle', 'midjourney', 'stable', 'flux', 'imagen'],
    '图生图': ['image', 'vision', 'multimodal'],
    '语音合成': ['tts', 'speech', 'voice'],
    '语音识别': ['asr', 'speech', 'whisper'],
    '代码生成': ['code', 'coder', 'copilot', 'programming'],
    '搜索': ['search', 'web', 'browse'],
    '信息检索': ['search', 'retrieval', 'rag'],
    '多模态': ['vision', 'multimodal', 'vl', 'omni'],
    '嵌入模型': ['embedding', 'embed'],
    '免费': ['free', 'fovt', '公益'],
    '推荐': ['recommend', 'popular', 'featured'],
}

# 厂商标签映射 - 根据匹配到的图标文件推断厂商标签
VENDOR_TAGS = {
    'openai': ['openai'],
    'claude': ['claude'],
    'anthropic': ['anthropic'],
    'gemini': ['gemini'],
    'google': ['google'],
    'palm': ['google', 'palm'],  # PaLM 模型属于 Google
    'qwen': ['qwen'],
    'alibaba': ['alibaba'],
    'deepseek': ['deepseek'],
    'chatglm': ['chatglm'],
    'zhipu': ['zhipu'],
    'kimi': ['kimi'],
    'moonshot': ['moonshot'],
    'grok': ['grok'],
    'meta': ['meta'],
    'mistral': ['mistral'],
    'cohere': ['cohere'],
    'yi': ['yi'],
    'baichuan': ['baichuan'],
    'microsoft': ['microsoft'],
    'siliconcloud': ['硅基流动'],
}

# 特殊处理规则
SPECIAL_RULES = {
    # 硅基流动的特殊处理
    'siliconcloud': {
        'url_pattern': 'siliconcloud-color.png',
        'tags': ['硅基流动']
    },
    # 当贝的特殊处理
    'dangbei': {
        'tags': ['当贝']
    },
    # FOVT公益的特殊处理
    'fovt': {
        'tags': ['fovt公益', '免费']
    }
}
