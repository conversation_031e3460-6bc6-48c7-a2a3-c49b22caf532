# 模型数据处理任务

## 任务概述
创建Python程序处理models-export-{数字最大的那个文件}.json中的模型数据，实现图标匹配和标签更新功能。

## 核心需求
1. 检查并更新git submodule: lobe-icons
2. 根据模型的name和id，到lobe-icons中的`packages/static-png/light/`下查找对应的模型图片
3. 以后缀为-color.png的优先，次选为无后缀的png
4. 对应修改模型的meta.profile_image_url
5. 根据模型修改tags
6. 保存修改后的models-export-{数字最大的那个文件}.json到models-export-mod.json

## 实施方案
采用增强功能实现方案，包含智能匹配和错误处理。

## 项目结构
```
model_processor/
├── main.py                 # 主程序入口
├── config.py              # 配置文件和映射规则
├── requirements.txt       # 依赖包列表
└── utils/
    ├── __init__.py
    ├── file_handler.py    # 文件操作工具
    ├── git_handler.py     # Git子模块操作
    ├── icon_matcher.py    # 智能图标匹配算法
    ├── tag_generator.py   # 智能标签生成器
    └── logger.py          # 统一日志系统
```

## 实施计划
1. 创建项目基础结构
2. 实现配置模块
3. 实现文件处理模块
4. 实现Git操作模块
5. 实现图标匹配模块
6. 实现标签生成模块
7. 实现日志系统
8. 实现主程序
9. 测试和优化

## 技术特点
- 多层级智能图标匹配算法
- 基于规则的标签生成系统
- 完善的错误处理和日志记录
- 模块化设计，易于维护和扩展
