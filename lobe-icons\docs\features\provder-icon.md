---
nav: Components
group: Features
title: ProviderIcon
apiHeader:
  docUrl: 'https://github.com/lobehub/lobe-icons/tree/master/docs/features/provider-icon.md'
  sourceUrl: 'https://github.com/lobehub/lobe-icons/tree/master/src/features/ProviderIcon/index.tsx'
---

## Default

<code src="./demos/provider-icon/index.tsx" center></code>

## Color

<code src="./demos/provider-icon/Color.tsx" center></code>

## Combine

<code src="./demos/provider-icon/Combine.tsx" center></code>

## Combine Color

<code src="./demos/provider-icon/CombineColor.tsx" center></code>

## Avatar

<code src="./demos/provider-icon/Avatar.tsx" center></code>

## Config

- **Enum:** <https://github.com/lobehub/lobe-icons/blob/master/src/features/providerEnum.ts>
- **Config:** <https://github.com/lobehub/lobe-icons/blob/master/src/features/providerConfig.tsx>
